/**
 * Email notification types and interfaces
 */

export type NotificationType =
  | 'auction_closed'
  | 'new_bid_received'
  | 'auction_winner_selected'
  | 'auction_started'
  | 'admin_auction_created'
  | 'admin_auction_closed'
  | 'admin_winners_selected'
  | 'admin_system_error'
  | 'winner_notification'
  | 'account_holder_winners';

export interface BaseNotificationData {
  type: NotificationType;
  auctionId: string;
  recipientEmail: string;
  recipientName?: string;
}

export interface AuctionClosedNotificationData extends BaseNotificationData {
  type: 'auction_closed';
  policyNumber: string;
  assetDisplayName: string;
  totalBids: number;
  closedAt: string;
  accountHolderName: string;
}

export interface NewBidNotificationData extends BaseNotificationData {
  type: 'new_bid_received';
  policyNumber: string;
  assetDisplayName: string;
  bidAmount: number;
  brokerName: string;
  timeRemaining: string;
  accountHolderName: string;
}

export interface AuctionWinnerNotificationData extends BaseNotificationData {
  type: 'auction_winner_selected';
  policyNumber: string;
  assetDisplayName: string;
  winningBidAmount: number;
  position: number; // 1st, 2nd, or 3rd place
  brokerName: string;
}

export interface AuctionStartedNotificationData extends BaseNotificationData {
  type: 'auction_started';
  policyNumber: string;
  assetDisplayName: string;
  startDate: string;
  endDate: string;
  accountHolderName: string;
}

export interface AdminAuctionNotificationData extends BaseNotificationData {
  type: 'admin_auction_created' | 'admin_auction_closed' | 'admin_winners_selected' | 'admin_system_error';
  policyNumber: string;
  policyId: string;
  assetDisplayName: string;
  executionTimestamp: string;
  processingDuration?: number;
  totalBids: number;
  participantCount: number;
  winnerSelectionCriteria?: string;
  adminDashboardUrl: string;
  errorDetails?: string;
  troubleshootingInfo?: string;
  systemMetrics?: {
    [key: string]: any;
  };
}

export interface WinnerNotificationData extends BaseNotificationData {
  type: 'winner_notification';
  policyNumber: string;
  assetDisplayName: string;
  winningPosition: number; // 1, 2, or 3
  winningBidAmount: number;
  brokerName: string;
  accountHolderContact: {
    name: string;
    email: string;
    phone?: string;
  };
  nextSteps: string;
}

export interface AccountHolderWinnersNotificationData extends BaseNotificationData {
  type: 'account_holder_winners';
  policyNumber: string;
  assetDisplayName: string;
  winners: Array<{
    position: number;
    brokerName: string;
    bidAmount: number;
    contact: {
      name: string;
      email: string;
      phone?: string;
    };
  }>;
  accountHolderName: string;
}

export type NotificationData = 
  | AuctionClosedNotificationData
  | NewBidNotificationData
  | AuctionWinnerNotificationData
  | AuctionStartedNotificationData;

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface NotificationQueueItem {
  id: string;
  data: NotificationData;
  attempts: number;
  maxAttempts: number;
  createdAt: string;
  scheduledFor?: string;
}
