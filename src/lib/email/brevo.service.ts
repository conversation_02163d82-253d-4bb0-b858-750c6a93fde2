/**
 * Brevo Email Service
 * Handles email notifications for auction events using Brevo SMTP API
 */

export interface EmailRecipient {
  email: string;
  name?: string;
}

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent?: string;
}

export interface AuctionClosedEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  totalBids: number;
  closedAt: string;
  accountHolderName: string;
}

export interface NewBidEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  bidAmount: number;
  brokerName: string;
  timeRemaining: string;
  accountHolderName: string;
}

export class BrevoEmailService {
  private apiKey: string;
  private senderEmail: string;
  private senderName: string;
  private baseUrl = 'https://api.brevo.com/v3';

  constructor() {
    this.apiKey = process.env.BREVO_API_KEY || '';
    this.senderEmail = process.env.BREVO_SENDER_EMAIL || '<EMAIL>';
    this.senderName = process.env.BREVO_SENDER_NAME || 'Zeeguros';

    if (!this.apiKey) {
      throw new Error('BREVO_API_KEY environment variable is required');
    }
  }

  /**
   * Send email using Brevo SMTP API
   */
  private async sendEmail(
    to: EmailRecipient[],
    template: EmailTemplate,
    replyTo?: EmailRecipient
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const payload = {
        sender: {
          name: this.senderName,
          email: this.senderEmail,
        },
        to: to.map(recipient => ({
          email: recipient.email,
          name: recipient.name || recipient.email,
        })),
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent || this.stripHtml(template.htmlContent),
        replyTo: replyTo ? {
          email: replyTo.email,
          name: replyTo.name || replyTo.email,
        } : undefined,
      };

      const response = await fetch(`${this.baseUrl}/smtp/email`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': this.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Brevo API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const result = await response.json();
      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      console.error('Failed to send email via Brevo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Strip HTML tags for text content
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  /**
   * Send auction closed notification to account holder
   */
  async sendAuctionClosedNotification(
    recipient: EmailRecipient,
    data: AuctionClosedEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const template: EmailTemplate = {
      subject: `Tu subasta ha finalizado - ${data.policyNumber}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Subasta Finalizada</h1>
          </div>
          
          <div style="padding: 30px; background-color: #f9f9f9;">
            <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              Tu subasta para la póliza <strong>${data.policyNumber}</strong> ha finalizado.
            </p>
            
            <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Subasta</h3>
              <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Total de ofertas:</strong> ${data.totalBids}</li>
                <li style="padding: 8px 0;"><strong>Finalizada el:</strong> ${data.closedAt}</li>
              </ul>
            </div>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              ${data.totalBids > 0 
                ? 'Puedes revisar las ofertas recibidas y seleccionar hasta 3 ganadores en tu panel de control.'
                : 'No se recibieron ofertas para esta subasta. Puedes crear una nueva subasta si lo deseas.'
              }
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/account-holder/auctions/${data.auctionId}" 
                 style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Ver Subasta
              </a>
            </div>
            
            <p style="font-size: 14px; color: #777; margin-top: 30px;">
              Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
            </p>
          </div>
          
          <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
          </div>
        </div>
      `,
    };

    return this.sendEmail([recipient], template);
  }

  /**
   * Send new bid notification to account holder
   */
  async sendNewBidNotification(
    recipient: EmailRecipient,
    data: NewBidEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const template: EmailTemplate = {
      subject: `Nueva oferta recibida - ${data.policyNumber}`,
      htmlContent: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Nueva Oferta Recibida</h1>
          </div>
          
          <div style="padding: 30px; background-color: #f9f9f9;">
            <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              Has recibido una nueva oferta para tu subasta de la póliza <strong>${data.policyNumber}</strong>.
            </p>
            
            <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Oferta</h3>
              <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Broker:</strong> ${data.brokerName}</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Oferta:</strong> €${data.bidAmount.toFixed(2)}</li>
                <li style="padding: 8px 0;"><strong>Tiempo restante:</strong> ${data.timeRemaining}</li>
              </ul>
            </div>
            
            <p style="font-size: 16px; line-height: 1.6; color: #555;">
              Puedes revisar todas las ofertas y hacer seguimiento de tu subasta en tu panel de control.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/account-holder/auctions/${data.auctionId}" 
                 style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                Ver Subasta
              </a>
            </div>
            
            <p style="font-size: 14px; color: #777; margin-top: 30px;">
              Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
            </p>
          </div>
          
          <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
          </div>
        </div>
      `,
    };

    return this.sendEmail([recipient], template);
  }

  /**
   * Test email connectivity
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/account`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'api-key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Brevo API test failed: ${response.status}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

// Export singleton instance
export const brevoEmailService = new BrevoEmailService();
