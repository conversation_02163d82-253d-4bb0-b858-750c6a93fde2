# Supabase Cron Jobs Testing Guide

## Overview

This document provides comprehensive testing procedures for the Supabase cron jobs implementation, including test cases for auction expiration, notification delivery, and edge cases.

## Pre-Test Setup

### 1. Verify Extensions
```sql
-- Check that required extensions are enabled
SELECT extname, extversion FROM pg_extension 
WHERE extname IN ('pg_cron', 'pg_net');
```

Expected result: Both `pg_cron` and `pg_net` should be listed with version numbers.

### 2. Verify Cron Jobs
```sql
-- Check that both cron jobs are scheduled
SELECT jobname, schedule, active FROM cron.job 
WHERE jobname IN ('close-expired-auctions', 'auction-notifications');
```

Expected result: Both jobs should be listed with `*/5 * * * *` schedule and `active = true`.

### 3. Verify Functions
```sql
-- Check that custom functions exist
SELECT proname, prosrc FROM pg_proc 
WHERE proname IN ('trigger_auction_notifications', 'close_expired_auctions_manual', 'trigger_notifications_manual');
```

Expected result: All three functions should be listed.

## Test Cases

### Test Case 1: Manual Auction Expiration

#### Purpose
Verify that the manual auction expiration function works correctly.

#### Test Steps
```sql
-- 1. Check current auction status
SELECT id, status, end_date FROM public.auction 
WHERE status = 'OPEN' 
ORDER BY end_date;

-- 2. Run manual expiration function
SELECT * FROM public.close_expired_auctions_manual();

-- 3. Verify results
-- If there were expired auctions, check they are now CLOSED
SELECT id, status, updated_at FROM public.auction 
WHERE updated_at >= NOW() - INTERVAL '1 minute'
AND status = 'CLOSED';
```

#### Expected Results
- Function returns count of expired auctions and their IDs
- Expired auctions (end_date <= NOW()) are updated to CLOSED status
- updated_at timestamp is current

### Test Case 2: Manual Notification Trigger

#### Purpose
Verify that the manual notification trigger function works correctly.

#### Test Steps
```sql
-- 1. Find a closed auction for testing
SELECT id FROM public.auction 
WHERE status = 'CLOSED' 
LIMIT 1;

-- 2. Trigger notification manually (replace with actual auction ID)
SELECT * FROM public.trigger_notifications_manual(ARRAY['your-auction-id-here']);

-- 3. Check notification log
SELECT * FROM public.notification_log 
WHERE auction_id = 'your-auction-id-here'
ORDER BY created_at DESC;
```

#### Expected Results
- Function returns success message with request ID
- HTTP request is logged in pg_net system
- Edge Function processes the request (check Supabase logs)

### Test Case 3: Cron Job Execution History

#### Purpose
Verify that cron jobs are executing on schedule.

#### Test Steps
```sql
-- Check recent cron job executions
SELECT jobname, start_time, end_time, return_message, status
FROM cron.job_run_details 
WHERE jobname IN ('close-expired-auctions', 'auction-notifications')
ORDER BY start_time DESC 
LIMIT 10;
```

#### Expected Results
- Jobs should execute every 5 minutes
- Status should be 'succeeded' for successful runs
- return_message should contain relevant information

### Test Case 4: Edge Function Connectivity

#### Purpose
Verify that the Edge Function is accessible and responding.

#### Test Steps
```bash
# Test Edge Function directly (replace with actual auction ID)
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"type": "auction_closed", "auctionIds": ["test-auction-id"]}' \
  https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification
```

#### Expected Results
- HTTP 200 response
- JSON response with success status
- Error handling for invalid auction IDs

### Test Case 5: Email Service Integration

#### Purpose
Verify that Brevo email service is working correctly.

#### Prerequisites
- Valid BREVO_API_KEY in environment
- Valid auction data in database

#### Test Steps
1. Create a test auction with valid account holder data
2. Close the auction manually
3. Trigger notification manually
4. Check email delivery

```sql
-- Create test scenario (adjust as needed)
-- This assumes you have test data in your database

-- 1. Find or create a test auction
SELECT a.id, p.policy_number, ah.user_id
FROM public.auction a
JOIN public.policy p ON a.policy_id = p.id
JOIN public.account_holder_profile ah ON a.account_holder_id = ah.id
WHERE a.status = 'OPEN'
LIMIT 1;

-- 2. Manually close the auction for testing
UPDATE public.auction 
SET status = 'CLOSED', updated_at = NOW()
WHERE id = 'your-test-auction-id';

-- 3. Trigger notification
SELECT * FROM public.trigger_notifications_manual(ARRAY['your-test-auction-id']);
```

#### Expected Results
- Email sent to account holder
- Notification logged in notification_log table
- Email received in inbox (check manually)

## Edge Case Testing

### Edge Case 1: No Expired Auctions

#### Test Steps
```sql
-- Ensure no auctions are expired
SELECT COUNT(*) FROM public.auction 
WHERE status = 'OPEN' AND end_date <= NOW();

-- Run manual expiration
SELECT * FROM public.close_expired_auctions_manual();
```

#### Expected Results
- Count should be 0
- Function should return `closed_count: 0` and empty array

### Edge Case 2: No Recently Closed Auctions

#### Test Steps
```sql
-- Check for recently closed auctions
SELECT COUNT(*) FROM public.auction 
WHERE status = 'CLOSED' 
AND updated_at >= NOW() - INTERVAL '5 minutes';

-- Run notification trigger
SELECT public.trigger_auction_notifications();
```

#### Expected Results
- Function should complete without errors
- Log message: "No recently closed auctions found for notifications"

### Edge Case 3: Invalid Auction Data

#### Test Steps
```sql
-- Test with non-existent auction ID
SELECT * FROM public.trigger_notifications_manual(ARRAY['non-existent-id']);
```

#### Expected Results
- Function should handle gracefully
- Error should be logged but not crash the system

### Edge Case 4: Network Connectivity Issues

#### Test Steps
1. Temporarily disable internet connection (if testing locally)
2. Run notification trigger
3. Check error handling

#### Expected Results
- pg_net should handle network errors gracefully
- Errors should be logged in notification_log
- System should continue functioning

## Performance Testing

### Test Case 1: Large Number of Expired Auctions

#### Purpose
Verify system performance with many expired auctions.

#### Test Steps
```sql
-- Check current performance baseline
EXPLAIN ANALYZE 
SELECT id FROM public.auction 
WHERE status = 'OPEN' AND end_date <= NOW();

-- Time the manual expiration function
\timing on
SELECT * FROM public.close_expired_auctions_manual();
\timing off
```

#### Expected Results
- Query should complete within reasonable time (< 1 second for 1000 auctions)
- Function should handle batch updates efficiently

### Test Case 2: Concurrent Cron Job Execution

#### Purpose
Verify that concurrent executions don't cause issues.

#### Test Steps
1. Monitor cron job execution times
2. Check for overlapping executions
3. Verify data consistency

```sql
-- Check for overlapping executions
SELECT jobname, start_time, end_time, 
       EXTRACT(EPOCH FROM (end_time - start_time)) as duration_seconds
FROM cron.job_run_details 
WHERE jobname IN ('close-expired-auctions', 'auction-notifications')
ORDER BY start_time DESC 
LIMIT 20;
```

#### Expected Results
- No overlapping executions
- Consistent execution times
- No data corruption or race conditions

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Cron Job Success Rate**
```sql
SELECT jobname, 
       COUNT(*) as total_runs,
       COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successful_runs,
       ROUND(COUNT(CASE WHEN status = 'succeeded' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM cron.job_run_details 
WHERE start_time >= NOW() - INTERVAL '24 hours'
GROUP BY jobname;
```

2. **Notification Delivery Rate**
```sql
SELECT notification_type,
       COUNT(*) as total_notifications,
       COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_notifications,
       ROUND(COUNT(CASE WHEN status = 'sent' THEN 1 END) * 100.0 / COUNT(*), 2) as delivery_rate
FROM public.notification_log 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY notification_type;
```

3. **Average Processing Time**
```sql
SELECT jobname,
       AVG(EXTRACT(EPOCH FROM (end_time - start_time))) as avg_duration_seconds,
       MAX(EXTRACT(EPOCH FROM (end_time - start_time))) as max_duration_seconds
FROM cron.job_run_details 
WHERE start_time >= NOW() - INTERVAL '24 hours'
GROUP BY jobname;
```

### Alerting Thresholds

- **Cron Job Failure**: Alert if success rate < 95% over 1 hour
- **Notification Failure**: Alert if delivery rate < 90% over 1 hour
- **Processing Time**: Alert if average duration > 30 seconds
- **Queue Buildup**: Alert if notification_log has > 100 pending items

## Troubleshooting Checklist

When issues occur, check in this order:

1. **Cron Job Status**: Are jobs scheduled and active?
2. **Function Execution**: Are custom functions working manually?
3. **Database Connectivity**: Can functions access required tables?
4. **Network Connectivity**: Can pg_net reach the Edge Function?
5. **Edge Function Status**: Is the function deployed and responding?
6. **Email Service**: Is Brevo API accessible and quota available?
7. **Environment Variables**: Are all required variables set correctly?

## Test Automation

### Automated Test Script

Create a script to run basic tests automatically:

```sql
-- Basic health check script
DO $$
DECLARE
    test_result RECORD;
    error_count INTEGER := 0;
BEGIN
    -- Test 1: Check extensions
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        RAISE WARNING 'TEST FAILED: pg_cron extension not found';
        error_count := error_count + 1;
    END IF;
    
    -- Test 2: Check cron jobs
    IF NOT EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'close-expired-auctions') THEN
        RAISE WARNING 'TEST FAILED: close-expired-auctions job not found';
        error_count := error_count + 1;
    END IF;
    
    -- Test 3: Test manual function
    SELECT * INTO test_result FROM public.close_expired_auctions_manual();
    RAISE NOTICE 'Manual expiration test: % auctions processed', test_result.closed_count;
    
    -- Summary
    IF error_count = 0 THEN
        RAISE NOTICE 'ALL TESTS PASSED: Cron job system is healthy';
    ELSE
        RAISE WARNING 'TESTS FAILED: % errors found', error_count;
    END IF;
END $$;
```

## Conclusion

Regular testing of the cron job system ensures reliable auction management and notification delivery. Run these tests:

- **Daily**: Automated health checks
- **Weekly**: Manual function testing
- **Monthly**: Performance and edge case testing
- **After Changes**: Full test suite execution

Remember to test in a staging environment before deploying changes to production.
