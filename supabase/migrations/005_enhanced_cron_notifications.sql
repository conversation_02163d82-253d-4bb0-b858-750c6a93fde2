-- Enhanced Cron Job for Comprehensive Auction Notifications
-- Updates the notification system to handle:
-- - Auction creation notifications (admin)
-- - Auction closure notifications (admin + account holder + automatic winner selection)
-- - System error notifications

-- Remove existing cron job to replace with enhanced version
SELECT cron.unschedule('auction-notifications');

-- Create enhanced function to handle all auction notification events
CREATE OR REPLACE FUNCTION public.trigger_comprehensive_auction_notifications()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    closed_auction_ids UUID[];
    created_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- Get the Supabase project URL and service role key
    edge_function_url := current_setting('app.supabase_url', true) || '/functions/v1/sendAuctionNotification';
    service_role_key := current_setting('app.supabase_service_role_key', true);
    
    -- If the setting is not available, use a default pattern
    IF edge_function_url IS NULL OR edge_function_url = '/functions/v1/sendAuctionNotification' THEN
        edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';
    END IF;

    -- Find auctions that were created in the last 5 minutes (for admin notifications)
    SELECT ARRAY_AGG(id) INTO created_auction_ids
    FROM public.auction
    WHERE status = 'OPEN'::auction_state
      AND created_at >= NOW() - INTERVAL '5 minutes'
      AND created_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl 
          WHERE nl.auction_id = auction.id 
            AND nl.notification_type = 'admin_auction_created'
            AND nl.status = 'sent'
      );

    -- Find auctions that were closed in the last 5 minutes
    SELECT ARRAY_AGG(id) INTO closed_auction_ids
    FROM public.auction
    WHERE status = 'CLOSED'::auction_state
      AND updated_at >= NOW() - INTERVAL '5 minutes'
      AND updated_at <= NOW()
      -- Only notify for auctions that haven't been notified yet
      AND NOT EXISTS (
          SELECT 1 FROM public.notification_log nl 
          WHERE nl.auction_id = auction.id 
            AND nl.notification_type = 'auction_closed'
            AND nl.status = 'sent'
      );

    -- Process auction creation notifications (admin only)
    IF created_auction_ids IS NOT NULL AND array_length(created_auction_ids, 1) > 0 THEN
        -- Log the notification attempt
        INSERT INTO public.notification_log (
            auction_id, 
            notification_type, 
            recipient_email, 
            recipient_type, 
            status,
            notification_data
        )
        SELECT 
            unnest(created_auction_ids),
            'admin_auction_created',
            COALESCE(current_setting('app.admin_email', true), '<EMAIL>'),
            'admin',
            'pending',
            jsonb_build_object('trigger_time', NOW(), 'auction_count', array_length(created_auction_ids, 1))
        ;

        -- Prepare the request payload for auction creation
        request_payload := jsonb_build_object(
            'type', 'auction_created',
            'auctionIds', to_jsonb(created_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || service_role_key
                ),
                body := request_payload
            ) INTO response_id;

            -- Update notification log with success
            UPDATE public.notification_log 
            SET status = 'sent', sent_at = NOW()
            WHERE auction_id = ANY(created_auction_ids)
              AND notification_type = 'admin_auction_created'
              AND status = 'pending';

        EXCEPTION WHEN OTHERS THEN
            -- Update notification log with failure
            UPDATE public.notification_log 
            SET status = 'failed', error_message = SQLERRM
            WHERE auction_id = ANY(created_auction_ids)
              AND notification_type = 'admin_auction_created'
              AND status = 'pending';
              
            RAISE NOTICE 'Failed to send auction creation notifications: %', SQLERRM;
        END;
    END IF;

    -- Process auction closure notifications (comprehensive)
    IF closed_auction_ids IS NOT NULL AND array_length(closed_auction_ids, 1) > 0 THEN
        -- Log the notification attempt
        INSERT INTO public.notification_log (
            auction_id, 
            notification_type, 
            recipient_email, 
            recipient_type, 
            status,
            notification_data
        )
        SELECT 
            unnest(closed_auction_ids),
            'auction_closed',
            'system',
            'system',
            'pending',
            jsonb_build_object('trigger_time', NOW(), 'auction_count', array_length(closed_auction_ids, 1))
        ;

        -- Prepare the request payload for auction closure
        request_payload := jsonb_build_object(
            'type', 'auction_closed',
            'auctionIds', to_jsonb(closed_auction_ids)
        );

        -- Make HTTP request to Edge Function
        BEGIN
            SELECT net.http_post(
                url := edge_function_url,
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || service_role_key
                ),
                body := request_payload
            ) INTO response_id;

            -- Update notification log with success
            UPDATE public.notification_log 
            SET status = 'sent', sent_at = NOW()
            WHERE auction_id = ANY(closed_auction_ids)
              AND notification_type = 'auction_closed'
              AND status = 'pending';

        EXCEPTION WHEN OTHERS THEN
            -- Update notification log with failure
            UPDATE public.notification_log 
            SET status = 'failed', error_message = SQLERRM
            WHERE auction_id = ANY(closed_auction_ids)
              AND notification_type = 'auction_closed'
              AND status = 'pending';
              
            RAISE NOTICE 'Failed to send auction closure notifications: %', SQLERRM;
        END;
    END IF;

    -- Log the cron job execution
    IF created_auction_ids IS NOT NULL OR closed_auction_ids IS NOT NULL THEN
        RAISE NOTICE 'Comprehensive auction notifications triggered: % created, % closed', 
            COALESCE(array_length(created_auction_ids, 1), 0),
            COALESCE(array_length(closed_auction_ids, 1), 0);
    END IF;

EXCEPTION WHEN OTHERS THEN
    -- Log any unexpected errors
    INSERT INTO public.notification_log (
        notification_type, 
        recipient_email, 
        recipient_type, 
        status,
        error_message,
        notification_data
    ) VALUES (
        'admin_system_error',
        COALESCE(current_setting('app.admin_email', true), '<EMAIL>'),
        'admin',
        'failed',
        'Cron job error: ' || SQLERRM,
        jsonb_build_object('error_time', NOW(), 'function', 'trigger_comprehensive_auction_notifications')
    );
    
    RAISE NOTICE 'Critical error in auction notification cron job: %', SQLERRM;
END;
$$;

-- Schedule the enhanced comprehensive auction notification cron job
-- Runs every 5 minutes to check for new auction events
SELECT cron.schedule(
    'comprehensive-auction-notifications',    -- Job name
    '*/5 * * * *',                           -- Cron expression: every 5 minutes
    'SELECT public.trigger_comprehensive_auction_notifications();'
);

-- Create a manual trigger function for testing
CREATE OR REPLACE FUNCTION public.trigger_comprehensive_notifications_manual(
    auction_ids UUID[] DEFAULT NULL,
    notification_type TEXT DEFAULT 'auction_closed'
)
RETURNS TABLE(success BOOLEAN, message TEXT, auction_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    target_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
    service_role_key TEXT;
BEGIN
    -- If no auction IDs provided, find recent auctions based on type
    IF auction_ids IS NULL THEN
        IF notification_type = 'auction_created' THEN
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'OPEN'::auction_state
              AND created_at >= NOW() - INTERVAL '1 hour'
            LIMIT 10;
        ELSE
            SELECT ARRAY_AGG(id) INTO target_auction_ids
            FROM public.auction
            WHERE status = 'CLOSED'::auction_state
              AND updated_at >= NOW() - INTERVAL '1 hour'
            LIMIT 10;
        END IF;
    ELSE
        target_auction_ids := auction_ids;
    END IF;

    -- Check if we have auctions to process
    IF target_auction_ids IS NULL OR array_length(target_auction_ids, 1) = 0 THEN
        RETURN QUERY SELECT false, 'No auctions found for notification type: ' || notification_type, 0;
        RETURN;
    END IF;

    -- Get configuration
    edge_function_url := current_setting('app.supabase_url', true) || '/functions/v1/sendAuctionNotification';
    service_role_key := current_setting('app.supabase_service_role_key', true);
    
    IF edge_function_url IS NULL OR edge_function_url = '/functions/v1/sendAuctionNotification' THEN
        edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';
    END IF;

    -- Prepare request payload
    request_payload := jsonb_build_object(
        'type', notification_type,
        'auctionIds', to_jsonb(target_auction_ids)
    );

    -- Make HTTP request
    BEGIN
        SELECT net.http_post(
            url := edge_function_url,
            headers := jsonb_build_object(
                'Content-Type', 'application/json',
                'Authorization', 'Bearer ' || service_role_key
            ),
            body := request_payload
        ) INTO response_id;

        RETURN QUERY SELECT true, 'Notifications triggered successfully', array_length(target_auction_ids, 1);
        
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Failed to trigger notifications: ' || SQLERRM, array_length(target_auction_ids, 1);
    END;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.trigger_comprehensive_auction_notifications TO authenticated;
GRANT EXECUTE ON FUNCTION public.trigger_comprehensive_notifications_manual TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION public.trigger_comprehensive_auction_notifications IS 
'Enhanced cron job function that handles comprehensive auction lifecycle notifications including admin notifications for auction creation and closure, automatic winner selection, and system error handling.';

COMMENT ON FUNCTION public.trigger_comprehensive_notifications_manual IS 
'Manual trigger function for testing comprehensive auction notifications. Supports both auction creation and closure notification types.';

-- Log the successful creation of the enhanced cron job
DO $$
BEGIN
    RAISE NOTICE 'Enhanced comprehensive auction notification cron job created successfully. Job name: comprehensive-auction-notifications, Schedule: every 5 minutes';
END $$;
