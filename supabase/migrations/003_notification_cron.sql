-- Cron Job 2: Notification Trigger
-- Find auctions that changed to CLOSED in the last 5 minutes and trigger notifications
-- This uses pg_net to call the Supabase Edge Function for email sending

-- Remove any existing notification cron job to avoid duplicates
SELECT cron.unschedule('auction-notifications');

-- Create a function to handle notification logic
CREATE OR REPLACE FUNCTION public.trigger_auction_notifications()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    closed_auction_ids UUID[];
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
BEGIN
    -- Get the Supabase project URL for the Edge Function
    -- This should be set as a database setting or environment variable
    edge_function_url := current_setting('app.supabase_url', true) || '/functions/v1/sendAuctionNotification';
    
    -- If the setting is not available, use a default pattern
    IF edge_function_url IS NULL OR edge_function_url = '/functions/v1/sendAuctionNotification' THEN
        edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';
    END IF;

    -- Find auctions that were closed in the last 5 minutes
    -- We check updated_at to catch recently closed auctions
    SELECT ARRAY_AGG(id) INTO closed_auction_ids
    FROM public.auction
    WHERE status = 'CLOSED'::auction_state
      AND updated_at >= NOW() - INTERVAL '5 minutes'
      AND updated_at <= NOW();

    -- If there are closed auctions, trigger notifications
    IF closed_auction_ids IS NOT NULL AND array_length(closed_auction_ids, 1) > 0 THEN
        -- Prepare the request payload
        request_payload := jsonb_build_object(
            'type', 'auction_closed',
            'auctionIds', to_jsonb(closed_auction_ids)
        );

        -- Make HTTP request to Edge Function using pg_net
        SELECT net.http_post(
            url := edge_function_url,
            headers := jsonb_build_object(
                'Content-Type', 'application/json',
                'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key', true)
            ),
            body := request_payload
        ) INTO response_id;

        -- Log the notification attempt
        RAISE NOTICE 'Triggered notifications for % closed auctions. Request ID: %', 
                     array_length(closed_auction_ids, 1), response_id;
    ELSE
        -- Log when no notifications are needed
        RAISE NOTICE 'No recently closed auctions found for notifications';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        -- Log any errors but don't fail the cron job
        RAISE WARNING 'Error in trigger_auction_notifications: %', SQLERRM;
END;
$$;

-- Grant execute permission on the notification function
GRANT EXECUTE ON FUNCTION public.trigger_auction_notifications() TO postgres;

-- Schedule the notification cron job
-- Runs every 5 minutes to check for recently closed auctions
SELECT cron.schedule(
    'auction-notifications',            -- Job name
    '*/5 * * * *',                     -- Cron expression: every 5 minutes
    'SELECT public.trigger_auction_notifications();'
);

-- Create a table to track notification attempts (optional, for monitoring)
CREATE TABLE IF NOT EXISTS public.notification_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auction_id UUID REFERENCES public.auction(id),
    notification_type TEXT NOT NULL,
    status TEXT NOT NULL, -- 'pending', 'sent', 'failed'
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for the notification log
CREATE INDEX IF NOT EXISTS idx_notification_log_auction_id ON public.notification_log(auction_id);
CREATE INDEX IF NOT EXISTS idx_notification_log_status ON public.notification_log(status);
CREATE INDEX IF NOT EXISTS idx_notification_log_created_at ON public.notification_log(created_at);

-- Add RLS policy for notification log (admin access only)
ALTER TABLE public.notification_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admin can view notification logs" ON public.notification_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public."User" u 
            WHERE u.id = auth.uid() AND u.role = 'ADMIN'
        )
    );

-- Create a function to manually trigger notifications for testing
CREATE OR REPLACE FUNCTION public.trigger_notifications_manual(auction_ids UUID[])
RETURNS TABLE(success BOOLEAN, message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    edge_function_url TEXT;
    request_payload JSONB;
    response_id BIGINT;
BEGIN
    -- Get the Edge Function URL
    edge_function_url := 'https://etzouzmoluegjbfikshb.supabase.co/functions/v1/sendAuctionNotification';

    -- Prepare the request payload
    request_payload := jsonb_build_object(
        'type', 'auction_closed',
        'auctionIds', to_jsonb(auction_ids)
    );

    -- Make HTTP request to Edge Function
    SELECT net.http_post(
        url := edge_function_url,
        headers := jsonb_build_object(
            'Content-Type', 'application/json',
            'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key', true)
        ),
        body := request_payload
    ) INTO response_id;

    RETURN QUERY SELECT true, 'Notifications triggered successfully. Request ID: ' || response_id;

EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT false, 'Error triggering notifications: ' || SQLERRM;
END;
$$;

-- Grant execute permission on the manual trigger function
GRANT EXECUTE ON FUNCTION public.trigger_notifications_manual(UUID[]) TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION public.trigger_auction_notifications() IS 
'Automated function that runs every 5 minutes to find recently closed auctions and trigger email notifications via Edge Function.';

COMMENT ON FUNCTION public.trigger_notifications_manual(UUID[]) IS 
'Manual function to trigger notifications for specific auction IDs. Used for testing and emergency situations.';

COMMENT ON TABLE public.notification_log IS 
'Log table to track notification attempts and their status for monitoring and debugging purposes.';

-- Log the successful creation of the notification cron job
DO $$
BEGIN
    RAISE NOTICE 'Auction notification cron job scheduled successfully. Job name: auction-notifications, Schedule: every 5 minutes';
END $$;
