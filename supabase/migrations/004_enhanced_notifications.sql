-- Enhanced Notification System Migration
-- Adds support for comprehensive auction lifecycle notifications including:
-- - Admin notifications for all auction events
-- - Winner notifications and automatic selection
-- - Account holder winner summaries

-- Add new columns to notification_log table to support enhanced notifications
ALTER TABLE public.notification_log 
ADD COLUMN IF NOT EXISTS recipient_email TEXT,
ADD COLUMN IF NOT EXISTS recipient_type TEXT CHECK (recipient_type IN ('account_holder', 'broker', 'admin')),
ADD COLUMN IF NOT EXISTS message_id TEXT,
ADD COLUMN IF NOT EXISTS processing_duration INTEGER,
ADD COLUMN IF NOT EXISTS notification_data JSONB;

-- Create enum for notification types if it doesn't exist
DO $$ BEGIN
    CREATE TYPE notification_type_enum AS ENUM (
        'auction_closed',
        'new_bid_received', 
        'auction_winner_selected',
        'auction_started',
        'admin_auction_created',
        'admin_auction_closed',
        'admin_winners_selected',
        'admin_system_error',
        'winner_notification',
        'account_holder_winners'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update notification_type column to use enum (if not already)
-- First, update existing data to match enum values
UPDATE public.notification_log 
SET notification_type = CASE 
    WHEN notification_type = 'auction_closed' THEN 'auction_closed'
    WHEN notification_type = 'new_bid' THEN 'new_bid_received'
    ELSE notification_type
END;

-- Add constraint to ensure notification_type matches enum values
ALTER TABLE public.notification_log 
DROP CONSTRAINT IF EXISTS notification_log_notification_type_check;

ALTER TABLE public.notification_log 
ADD CONSTRAINT notification_log_notification_type_check 
CHECK (notification_type IN (
    'auction_closed',
    'new_bid_received', 
    'auction_winner_selected',
    'auction_started',
    'admin_auction_created',
    'admin_auction_closed',
    'admin_winners_selected',
    'admin_system_error',
    'winner_notification',
    'account_holder_winners'
));

-- Create additional indexes for performance
CREATE INDEX IF NOT EXISTS idx_notification_log_recipient_email ON public.notification_log(recipient_email);
CREATE INDEX IF NOT EXISTS idx_notification_log_recipient_type ON public.notification_log(recipient_type);
CREATE INDEX IF NOT EXISTS idx_notification_log_notification_type ON public.notification_log(notification_type);
CREATE INDEX IF NOT EXISTS idx_notification_log_sent_at ON public.notification_log(sent_at);

-- Add RLS policies for different recipient types
CREATE POLICY "Account holders can view their notifications" ON public.notification_log
    FOR SELECT USING (
        recipient_type = 'account_holder' AND 
        recipient_email = (
            SELECT u.email FROM public."User" u 
            WHERE u.id = auth.uid()
        )
    );

CREATE POLICY "Brokers can view their notifications" ON public.notification_log
    FOR SELECT USING (
        recipient_type = 'broker' AND 
        recipient_email = (
            SELECT u.email FROM public."User" u 
            WHERE u.id = auth.uid()
        )
    );

-- Function to log notifications (for use by Edge Functions)
CREATE OR REPLACE FUNCTION public.log_notification(
    p_auction_id UUID,
    p_notification_type TEXT,
    p_recipient_email TEXT,
    p_recipient_type TEXT,
    p_status TEXT DEFAULT 'pending',
    p_message_id TEXT DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_processing_duration INTEGER DEFAULT NULL,
    p_notification_data JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO public.notification_log (
        auction_id,
        notification_type,
        recipient_email,
        recipient_type,
        status,
        message_id,
        error_message,
        processing_duration,
        notification_data,
        created_at,
        sent_at
    ) VALUES (
        p_auction_id,
        p_notification_type,
        p_recipient_email,
        p_recipient_type,
        p_status,
        p_message_id,
        p_error_message,
        p_processing_duration,
        p_notification_data,
        NOW(),
        CASE WHEN p_status = 'sent' THEN NOW() ELSE NULL END
    )
    RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$;

-- Grant execute permission on the logging function
GRANT EXECUTE ON FUNCTION public.log_notification TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_notification TO service_role;

-- Function to get notification statistics (for admin dashboard)
CREATE OR REPLACE FUNCTION public.get_notification_stats(
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
    notification_type TEXT,
    recipient_type TEXT,
    total_sent INTEGER,
    total_failed INTEGER,
    success_rate NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        nl.notification_type,
        nl.recipient_type,
        COUNT(CASE WHEN nl.status = 'sent' THEN 1 END)::INTEGER as total_sent,
        COUNT(CASE WHEN nl.status = 'failed' THEN 1 END)::INTEGER as total_failed,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(CASE WHEN nl.status = 'sent' THEN 1 END)::NUMERIC / COUNT(*)::NUMERIC) * 100, 2)
            ELSE 0
        END as success_rate
    FROM public.notification_log nl
    WHERE nl.created_at >= p_start_date 
      AND nl.created_at <= p_end_date
    GROUP BY nl.notification_type, nl.recipient_type
    ORDER BY nl.notification_type, nl.recipient_type;
END;
$$;

-- Grant execute permission on the stats function to admins
GRANT EXECUTE ON FUNCTION public.get_notification_stats TO authenticated;

-- Add RLS policy for the stats function (admin only)
CREATE POLICY "Admin can access notification stats" ON public.notification_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public."User" u 
            WHERE u.id = auth.uid() AND u.role = 'ADMIN'
        )
    );

-- Add comment to document the enhanced notification system
COMMENT ON TABLE public.notification_log IS 
'Enhanced notification log supporting comprehensive auction lifecycle notifications including admin notifications, winner selections, and account holder summaries. Tracks all email notifications sent through the Brevo service.';

COMMENT ON FUNCTION public.log_notification IS 
'Logs notification attempts with comprehensive metadata for monitoring and debugging. Used by Edge Functions to track email delivery status.';

COMMENT ON FUNCTION public.get_notification_stats IS 
'Provides notification statistics for admin dashboard monitoring. Returns success rates and delivery metrics by notification and recipient type.';

-- Log the successful creation of the enhanced notification system
DO $$
BEGIN
    RAISE NOTICE 'Enhanced notification system migration completed successfully. Added support for admin notifications, winner selections, and comprehensive logging.';
END $$;
