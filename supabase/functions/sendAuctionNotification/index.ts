/**
 * Supabase Edge Function: sendAuctionNotification
 * Handles email notifications for auction events
 * Called by pg_net from the notification cron job
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Email service interfaces (simplified for Edge Function)
interface EmailRecipient {
  email: string;
  name?: string;
}

interface AuctionClosedEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  totalBids: number;
  closedAt: string;
  accountHolderName: string;
}

interface NewBidEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  bidAmount: number;
  brokerName: string;
  timeRemaining: string;
  accountHolderName: string;
}

class BrevoEmailService {
  private apiKey: string;
  private senderEmail: string;
  private senderName: string;
  private baseUrl = 'https://api.brevo.com/v3';

  constructor() {
    this.apiKey = Deno.env.get('BREVO_API_KEY') || '';
    this.senderEmail = Deno.env.get('BREVO_SENDER_EMAIL') || '<EMAIL>';
    this.senderName = Deno.env.get('BREVO_SENDER_NAME') || 'Zeeguros';

    if (!this.apiKey) {
      throw new Error('BREVO_API_KEY environment variable is required');
    }
  }

  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  private async sendEmail(
    to: EmailRecipient[],
    subject: string,
    htmlContent: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const payload = {
        sender: {
          name: this.senderName,
          email: this.senderEmail,
        },
        to: to.map(recipient => ({
          email: recipient.email,
          name: recipient.name || recipient.email,
        })),
        subject,
        htmlContent,
        textContent: this.stripHtml(htmlContent),
      };

      const response = await fetch(`${this.baseUrl}/smtp/email`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': this.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Brevo API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const result = await response.json();
      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      console.error('Failed to send email via Brevo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async sendAuctionClosedNotification(
    recipient: EmailRecipient,
    data: AuctionClosedEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const siteUrl = Deno.env.get('NEXT_PUBLIC_SITE_URL') || 'https://zeeguros.com';
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Subasta Finalizada</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
          
          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            Tu subasta para la póliza <strong>${data.policyNumber}</strong> ha finalizado.
          </p>
          
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Subasta</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Total de ofertas:</strong> ${data.totalBids}</li>
              <li style="padding: 8px 0;"><strong>Finalizada el:</strong> ${data.closedAt}</li>
            </ul>
          </div>
          
          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            ${data.totalBids > 0 
              ? 'Puedes revisar las ofertas recibidas y seleccionar hasta 3 ganadores en tu panel de control.'
              : 'No se recibieron ofertas para esta subasta. Puedes crear una nueva subasta si lo deseas.'
            }
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${siteUrl}/account-holder/auctions/${data.auctionId}" 
               style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Ver Subasta
            </a>
          </div>
          
          <p style="font-size: 14px; color: #777; margin-top: 30px;">
            Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
          </p>
        </div>
        
        <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
        </div>
      </div>
    `;

    return this.sendEmail([recipient], `Tu subasta ha finalizado - ${data.policyNumber}`, htmlContent);
  }

  async sendNewBidNotification(
    recipient: EmailRecipient,
    data: NewBidEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const siteUrl = Deno.env.get('NEXT_PUBLIC_SITE_URL') || 'https://zeeguros.com';
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Nueva Oferta Recibida</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
          
          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            Has recibido una nueva oferta para tu subasta de la póliza <strong>${data.policyNumber}</strong>.
          </p>
          
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Oferta</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Broker:</strong> ${data.brokerName}</li>
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Oferta:</strong> €${data.bidAmount.toFixed(2)}</li>
              <li style="padding: 8px 0;"><strong>Tiempo restante:</strong> ${data.timeRemaining}</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${siteUrl}/account-holder/auctions/${data.auctionId}" 
               style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Ver Subasta
            </a>
          </div>
          
          <p style="font-size: 14px; color: #777; margin-top: 30px;">
            Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
          </p>
        </div>
        
        <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
        </div>
      </div>
    `;

    return this.sendEmail([recipient], `Nueva oferta recibida - ${data.policyNumber}`, htmlContent);
  }
}

serve(async (req) => {
  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify request method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { type, auctionIds } = await req.json()

    if (!type || !auctionIds || !Array.isArray(auctionIds)) {
      return new Response(
        JSON.stringify({ error: 'Invalid request body. Expected type and auctionIds array.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Initialize email service
    const emailService = new BrevoEmailService()

    const results = []

    for (const auctionId of auctionIds) {
      try {
        if (type === 'auction_closed') {
          // Get auction details with related data
          const { data: auction, error } = await supabase
            .from('auction')
            .select(`
              id,
              policy!inner(
                policy_number,
                account_holder:account_holder_id(
                  user!inner(email, first_name, last_name, display_name)
                ),
                asset(
                  asset_type,
                  vehicle(brand, model, year)
                )
              ),
              bids:bid(id),
              updated_at
            `)
            .eq('id', auctionId)
            .eq('status', 'CLOSED')
            .single()

          if (error || !auction) {
            console.error(`Failed to fetch auction ${auctionId}:`, error)
            results.push({ auctionId, success: false, error: 'Auction not found' })
            continue
          }

          // Prepare email data
          const accountHolder = auction.policy.account_holder
          const user = accountHolder.user
          const asset = auction.policy.asset
          const vehicle = asset?.vehicle

          const emailData: AuctionClosedEmailData = {
            auctionId: auction.id,
            policyNumber: auction.policy.policy_number || 'N/A',
            assetDisplayName: vehicle 
              ? `${vehicle.brand} ${vehicle.model} (${vehicle.year})`
              : `${asset?.asset_type || 'Activo'}`,
            totalBids: auction.bids.length,
            closedAt: new Date(auction.updated_at).toLocaleString('es-ES'),
            accountHolderName: user.display_name || `${user.first_name} ${user.last_name}`.trim() || 'Usuario',
          }

          // Send email
          const emailResult = await emailService.sendAuctionClosedNotification(
            { email: user.email, name: emailData.accountHolderName },
            emailData
          )

          results.push({
            auctionId,
            success: emailResult.success,
            messageId: emailResult.messageId,
            error: emailResult.error,
          })
        }
      } catch (error) {
        console.error(`Error processing auction ${auctionId}:`, error)
        results.push({
          auctionId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        processed: results.length,
        results,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
